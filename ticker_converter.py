"""
Ticker Converter Module

This module provides comprehensive ticker symbol conversion between different data sources
(Yahoo Finance, Schwab API, etc.) to ensure proper symbol formatting for each API.
"""

import logging

logger = logging.getLogger(__name__)

class TickerConverter:
    """
    Handles ticker symbol conversion between different data sources.

    Supports conversion between:
    - Yahoo Finance format (ES=F, ^SPX, etc.)
    - Schwab API format (/ES, SPX, etc.)
    - Standard format (ES, SPX, etc.)
    """

    # TradingView to Yahoo Finance futures mappings
    TRADINGVIEW_TO_YAHOO_FUTURES = {
        # E-mini S&P 500
        'ES1!': 'ES=F',
        'MES1!': 'ES=F',  # Micro E-mini S&P 500 maps to same Yahoo symbol

        # E-mini NASDAQ-100
        'NQ1!': 'NQ=F',
        'MNQ1!': 'NQ=F',  # Micro E-mini NASDAQ-100 maps to same Yahoo symbol

        # E-mini Dow Jones
        'YM1!': 'YM=F',
        'MYM1!': 'YM=F',  # Micro E-mini Dow Jones maps to same Yahoo symbol

        # E-mini Russell 2000
        'RTY1!': 'RTY=F',
        'M2K1!': 'RTY=F',  # Micro E-mini Russell 2000 maps to same Yahoo symbol

        # Energy
        'CL1!': 'CL=F',    # Crude Oil
        'MCL1!': 'CL=F',   # Micro Crude Oil
        'NG1!': 'NG=F',    # Natural Gas
        'RB1!': 'RB=F',    # RBOB Gasoline
        'HO1!': 'HO=F',    # Heating Oil

        # Metals
        'GC1!': 'GC=F',    # Gold
        'MGC1!': 'GC=F',   # Micro Gold
        'SI1!': 'SI=F',    # Silver
        'HG1!': 'HG=F',    # Copper
        'PA1!': 'PA=F',    # Palladium
        'PL1!': 'PL=F',    # Platinum

        # Treasuries
        'ZB1!': 'ZB=F',    # 30-Year Treasury Bond
        'ZN1!': 'ZN=F',    # 10-Year Treasury Note
        'ZF1!': 'ZF=F',    # 5-Year Treasury Note
        'ZT1!': 'ZT=F',    # 2-Year Treasury Note
        'UB1!': 'UB=F',    # Ultra Treasury Bond

        # Agriculture
        'ZC1!': 'ZC=F',    # Corn
        'ZS1!': 'ZS=F',    # Soybeans
        'ZW1!': 'ZW=F',    # Wheat
        'ZM1!': 'ZM=F',    # Soybean Meal
        'ZL1!': 'ZL=F',    # Soybean Oil
        'ZO1!': 'ZO=F',    # Oats
        'ZR1!': 'ZR=F',    # Rough Rice
        'KC1!': 'KC=F',    # Coffee
        'SB1!': 'SB=F',    # Sugar
        'CT1!': 'CT=F',    # Cotton
        'CC1!': 'CC=F',    # Cocoa
        'LB1!': 'LB=F',    # Lumber

        # Livestock
        'HE1!': 'HE=F',    # Lean Hogs
        'LE1!': 'LE=F',    # Live Cattle
        'GF1!': 'GF=F',    # Feeder Cattle

        # Currency
        'DX1!': 'DX=F',    # US Dollar Index
        'EUR1!': '6E=F',   # Euro
        'GBP1!': '6B=F',   # British Pound
        'JPY1!': '6J=F',   # Japanese Yen
        'CAD1!': '6C=F',   # Canadian Dollar
        'CHF1!': '6S=F',   # Swiss Franc
        'AUD1!': '6A=F',   # Australian Dollar

        # Crypto
        'BTC1!': 'BTC=F',  # Bitcoin Futures
        'ETH1!': 'ETH=F',  # Ethereum Futures

        # Volatility
        'VIX1!': '^VIX',   # VIX Index (not futures, but commonly used)
    }

    # Futures symbol mappings (Yahoo Finance -> Schwab API)
    FUTURES_MAPPINGS = {
        'ES=F': '/ES',      # E-mini S&P 500
        'NQ=F': '/NQ',      # E-mini NASDAQ-100
        'YM=F': '/YM',      # E-mini Dow Jones
        'RTY=F': '/RTY',    # E-mini Russell 2000
        'CL=F': '/CL',      # Crude Oil
        'GC=F': '/GC',      # Gold
        'SI=F': '/SI',      # Silver
        'ZB=F': '/ZB',      # 30-Year Treasury Bond
        'ZN=F': '/ZN',      # 10-Year Treasury Note
        'ZF=F': '/ZF',      # 5-Year Treasury Note
        'ZT=F': '/ZT',      # 2-Year Treasury Note
        'NG=F': '/NG',      # Natural Gas
        'HG=F': '/HG',      # Copper
        'ZC=F': '/ZC',      # Corn
        'ZS=F': '/ZS',      # Soybeans
        'ZW=F': '/ZW',      # Wheat
        'KC=F': '/KC',      # Coffee
        'SB=F': '/SB',      # Sugar
        'CT=F': '/CT',      # Cotton
        'CC=F': '/CC',      # Cocoa
        'LB=F': '/LB',      # Lumber
        'HE=F': '/HE',      # Lean Hogs
        'LE=F': '/LE',      # Live Cattle
        'GF=F': '/GF',      # Feeder Cattle
        'PA=F': '/PA',      # Palladium
        'PL=F': '/PL',      # Platinum
        'DX=F': '/DX',      # US Dollar Index
        'BTC=F': '/BTC',    # Bitcoin Futures
        'ETH=F': '/ETH',    # Ethereum Futures
    }

    # Index symbol mappings
    INDEX_MAPPINGS = {
        # Yahoo Finance -> Schwab API
        '^SPX': 'SPX',      # S&P 500 Index
        '^GSPC': 'SPX',     # S&P 500 Index (alternative)
        'SPX': 'SPX',       # S&P 500 Index (bare)
        '^NDX': 'NDX',      # NASDAQ-100 Index
        '^IXIC': 'COMP',    # NASDAQ Composite
        '^DJI': 'DJI',      # Dow Jones Industrial Average
        '^RUT': 'RUT',      # Russell 2000 Index
        '^VIX': 'VIX',      # CBOE Volatility Index
        '^TNX': 'TNX',      # 10-Year Treasury Yield
        '^TYX': 'TYX',      # 30-Year Treasury Yield
        '^FVX': 'FVX',      # 5-Year Treasury Yield
        '^IRX': 'IRX',      # 13-Week Treasury Bill
    }

    # Month codes for futures contracts
    MONTH_CODES = {
        'F': 'January',   'G': 'February', 'H': 'March',    'J': 'April',
        'K': 'May',       'M': 'June',     'N': 'July',     'Q': 'August',
        'U': 'September', 'V': 'October',  'X': 'November', 'Z': 'December'
    }

    # TradingView specific contract month mappings to Yahoo Finance
    TRADINGVIEW_SPECIFIC_CONTRACTS = {
        # E-mini S&P 500 specific contracts
        'ESU2025': 'esu25.cme',  # September 2025
        'ESZ2025': 'esz25.cme',  # December 2025
        'ESH2025': 'esh25.cme',  # March 2025
        'ESM2025': 'esm25.cme',  # June 2025

        # Micro E-mini S&P 500 specific contracts
        'MESU2025': 'mesu25.cme',  # Micro E-mini S&P 500 September 2025
        'MESZ2025': 'mesz25.cme',  # Micro E-mini S&P 500 December 2025
        'MESH2025': 'mesh25.cme',  # Micro E-mini S&P 500 March 2025
        'MESM2025': 'mesm25.cme',  # Micro E-mini S&P 500 June 2025

        # E-mini NASDAQ-100 specific contracts
        'NQU2025': 'nqu25.cme',  # September 2025
        'NQZ2025': 'nqz25.cme',  # December 2025
        'NQH2025': 'nqh25.cme',  # March 2025
        'NQM2025': 'nqm25.cme',  # June 2025

        # Micro E-mini NASDAQ-100 specific contracts
        'MNQU2025': 'mnqu25.cme',  # Micro E-mini NASDAQ-100 September 2025
        'MNQZ2025': 'mnqz25.cme',  # Micro E-mini NASDAQ-100 December 2025
        'MNQH2025': 'mnqh25.cme',  # Micro E-mini NASDAQ-100 March 2025
        'MNQM2025': 'mnqm25.cme',  # Micro E-mini NASDAQ-100 June 2025

        # Euro futures (example for EMU2025)
        'EMU2025': 'emu25.cme',   # Euro September 2025
        'EMZ2025': 'emz25.cme',   # Euro December 2025
        'EMH2025': 'emh25.cme',   # Euro March 2025
        'EMM2025': 'emm25.cme',   # Euro June 2025
    }

    # Reverse mappings for conversion back
    REVERSE_FUTURES_MAPPINGS = {v: k for k, v in FUTURES_MAPPINGS.items()}
    REVERSE_INDEX_MAPPINGS = {v: k for k, v in INDEX_MAPPINGS.items()}
    REVERSE_TRADINGVIEW_FUTURES = {v: k for k, v in TRADINGVIEW_TO_YAHOO_FUTURES.items()}

    @classmethod
    def to_schwab_format(cls, symbol: str) -> str:
        """
        Convert a symbol to Schwab API format.

        Args:
            symbol: Input symbol (can be Yahoo Finance format, Schwab format, or standard)

        Returns:
            Symbol in Schwab API format
        """
        if not symbol:
            return symbol

        symbol = symbol.upper().strip()

        # Check if it's already in Schwab futures format (starts with /)
        if symbol.startswith('/'):
            return symbol

        # Check futures mappings
        if symbol in cls.FUTURES_MAPPINGS:
            converted = cls.FUTURES_MAPPINGS[symbol]
            logger.info(f"Converted futures symbol: {symbol} -> {converted}")
            return converted

        # Check index mappings
        if symbol in cls.INDEX_MAPPINGS:
            converted = cls.INDEX_MAPPINGS[symbol]
            logger.info(f"Converted index symbol: {symbol} -> {converted}")
            return converted

        # Handle common futures patterns
        if symbol.endswith('=F'):
            # Try to map common futures without explicit mapping
            base = symbol[:-2]  # Remove =F
            schwab_symbol = f'/{base}'
            logger.info(f"Pattern-based futures conversion: {symbol} -> {schwab_symbol}")
            return schwab_symbol

        # Check if it's a common futures symbol without suffix
        common_futures = ['ES', 'NQ', 'YM', 'RTY', 'CL', 'GC', 'SI', 'ZB', 'ZN', 'ZF', 'ZT', 'NG', 'HG']
        if symbol in common_futures:
            schwab_symbol = f'/{symbol}'
            logger.info(f"Common futures symbol conversion: {symbol} -> {schwab_symbol}")
            return schwab_symbol

        # Handle index patterns
        if symbol.startswith('^'):
            # Remove ^ prefix for indices
            base = symbol[1:]
            logger.info(f"Pattern-based index conversion: {symbol} -> {base}")
            return base

        # Return as-is if no conversion needed
        return symbol

    @classmethod
    def to_yahoo_format(cls, symbol: str) -> str:
        """
        Convert a symbol to Yahoo Finance format.

        Args:
            symbol: Input symbol (can be TradingView, Schwab format, Yahoo format, or standard)

        Returns:
            Symbol in Yahoo Finance format
        """
        if not symbol:
            return symbol

        symbol = symbol.upper().strip()

        # Check TradingView to Yahoo Finance mappings first
        if symbol in cls.TRADINGVIEW_TO_YAHOO_FUTURES:
            converted = cls.TRADINGVIEW_TO_YAHOO_FUTURES[symbol]
            logger.info(f"Converted TradingView to Yahoo futures format: {symbol} -> {converted}")
            return converted

        # Check TradingView specific contract mappings
        if symbol in cls.TRADINGVIEW_SPECIFIC_CONTRACTS:
            converted = cls.TRADINGVIEW_SPECIFIC_CONTRACTS[symbol]
            logger.info(f"Converted TradingView specific contract to Yahoo: {symbol} -> {converted}")
            return converted

        # Handle TradingView specific contract patterns (e.g., MESU2025, ESU2025)
        import re
        tv_contract_match = re.match(r'^([A-Z]+)([FGHJKMNQUVXZ])(\d{4})$', symbol)
        if tv_contract_match:
            base, month_code, year = tv_contract_match.groups()
            # Convert to Yahoo Finance format: basemonthyy.cme (e.g., esu25.cme, mesu25.cme)
            year_short = year[-2:]  # Get last 2 digits of year
            yahoo_symbol = f'{base.lower()}{month_code.lower()}{year_short}.cme'
            logger.info(f"Converted TradingView contract pattern to Yahoo: {symbol} -> {yahoo_symbol}")
            return yahoo_symbol

        # Check reverse futures mappings
        if symbol in cls.REVERSE_FUTURES_MAPPINGS:
            converted = cls.REVERSE_FUTURES_MAPPINGS[symbol]
            logger.info(f"Converted to Yahoo futures format: {symbol} -> {converted}")
            return converted

        # Check reverse index mappings
        if symbol in cls.REVERSE_INDEX_MAPPINGS:
            converted = cls.REVERSE_INDEX_MAPPINGS[symbol]
            logger.info(f"Converted to Yahoo index format: {symbol} -> {converted}")
            return converted

        # Handle Schwab futures format (starts with /)
        if symbol.startswith('/'):
            base = symbol[1:]  # Remove /
            yahoo_symbol = f'{base}=F'
            logger.info(f"Pattern-based conversion to Yahoo futures: {symbol} -> {yahoo_symbol}")
            return yahoo_symbol

        # Check if it's a common futures symbol without prefix
        common_futures = ['ES', 'NQ', 'YM', 'RTY', 'CL', 'GC', 'SI', 'ZB', 'ZN', 'ZF', 'ZT', 'NG', 'HG']
        if symbol in common_futures:
            yahoo_symbol = f'{symbol}=F'
            logger.info(f"Common futures symbol conversion to Yahoo: {symbol} -> {yahoo_symbol}")
            return yahoo_symbol

        # For indices, check if we need to add ^ prefix
        common_indices = ['SPX', 'NDX', 'DJI', 'RUT', 'VIX', 'COMP', 'TNX', 'TYX', 'FVX', 'IRX']
        if symbol in common_indices:
            # Special case for SPX - Yahoo uses ^SPX, not ^GSPC
            if symbol == 'SPX':
                yahoo_symbol = '^SPX'
            else:
                yahoo_symbol = f'^{symbol}'
            logger.info(f"Added ^ prefix for index: {symbol} -> {yahoo_symbol}")
            return yahoo_symbol

        # Return as-is if no conversion needed
        return symbol

    @classmethod
    def normalize_symbol(cls, symbol: str) -> str:
        """
        Normalize a symbol to a standard format (remove prefixes/suffixes).

        Args:
            symbol: Input symbol in any format

        Returns:
            Normalized symbol
        """
        if not symbol:
            return symbol

        symbol = symbol.upper().strip()

        # Remove common prefixes and suffixes
        if symbol.startswith('/'):
            symbol = symbol[1:]
        elif symbol.startswith('^'):
            symbol = symbol[1:]
        elif symbol.endswith('=F'):
            symbol = symbol[:-2]

        return symbol

    @classmethod
    def is_futures_symbol(cls, symbol: str) -> bool:
        """
        Check if a symbol represents a futures contract.

        Args:
            symbol: Symbol to check

        Returns:
            True if it's a futures symbol, False otherwise
        """
        if not symbol:
            return False

        symbol = symbol.upper().strip()

        # Check TradingView futures mappings
        if symbol in cls.TRADINGVIEW_TO_YAHOO_FUTURES or symbol in cls.TRADINGVIEW_SPECIFIC_CONTRACTS:
            return True

        # Check explicit mappings
        if symbol in cls.FUTURES_MAPPINGS or symbol in cls.REVERSE_FUTURES_MAPPINGS:
            return True

        # Check TradingView patterns
        import re
        if (symbol.endswith('1!') or  # TradingView continuous contracts (ES1!, NQ1!)
            re.match(r'^[A-Z]+[FGHJKMNQUVXZ]\d{4}$', symbol)):  # Specific contracts (ESU2025, MESU2025)
            return True

        # Check traditional patterns
        if symbol.startswith('/') or symbol.endswith('=F'):
            return True

        # Check if it's a common futures symbol
        common_futures = ['ES', 'NQ', 'YM', 'RTY', 'CL', 'GC', 'SI', 'ZB', 'ZN', 'ZF', 'ZT', 'NG', 'HG']
        if symbol in common_futures:
            return True

        return False

    @classmethod
    def parse_tradingview_futures(cls, symbol: str) -> dict:
        """
        Parse TradingView futures symbol to extract contract details.

        Args:
            symbol: TradingView futures symbol (e.g., ES1!, MESU2025, EMU2025)

        Returns:
            Dictionary with contract details
        """
        if not symbol:
            return {'original': symbol, 'type': 'unknown'}

        symbol = symbol.upper().strip()

        # Handle continuous contracts (ES1!, NQ1!, etc.)
        if symbol.endswith('1!'):
            base = symbol[:-2]  # Remove 1!
            return {
                'original': symbol,
                'base': base,
                'type': 'continuous',
                'month': None,
                'year': None,
                'month_name': None,
                'yahoo_equivalent': cls.TRADINGVIEW_TO_YAHOO_FUTURES.get(symbol, f'{base}=F')
            }

        # Handle specific contract months (MESU2025, ESU2025, EMU2025, etc.)
        import re
        match = re.match(r'^([A-Z]+)([FGHJKMNQUVXZ])(\d{4})$', symbol)
        if match:
            base, month_code, year = match.groups()
            month_name = cls.MONTH_CODES.get(month_code, "Unknown")
            year_short = year[-2:]  # Get last 2 digits

            # Check if we have a specific mapping
            yahoo_equivalent = cls.TRADINGVIEW_SPECIFIC_CONTRACTS.get(symbol)
            if not yahoo_equivalent:
                # Generate Yahoo format: basemonthyy.cme (e.g., esu25.cme, mesu25.cme)
                yahoo_equivalent = f'{base.lower()}{month_code.lower()}{year_short}.cme'

            return {
                'original': symbol,
                'base': base,
                'type': 'specific',
                'month': month_code,
                'year': year,
                'month_name': month_name,
                'yahoo_equivalent': yahoo_equivalent
            }

        # Not a recognized TradingView futures pattern
        return {
            'original': symbol,
            'type': 'unknown',
            'base': symbol,
            'month': None,
            'year': None,
            'month_name': None,
            'yahoo_equivalent': symbol
        }

    @classmethod
    def is_index_symbol(cls, symbol: str) -> bool:
        """
        Check if a symbol represents an index.

        Args:
            symbol: Symbol to check

        Returns:
            True if it's an index symbol, False otherwise
        """
        if not symbol:
            return False

        symbol = symbol.upper().strip()

        # Check explicit mappings
        if symbol in cls.INDEX_MAPPINGS or symbol in cls.REVERSE_INDEX_MAPPINGS:
            return True

        # Check patterns
        if symbol.startswith('^'):
            return True

        # Check common indices
        common_indices = ['SPX', 'NDX', 'DJI', 'RUT', 'VIX', 'COMP', 'TNX', 'TYX', 'FVX', 'IRX']
        if symbol in common_indices:
            return True

        return False

    @classmethod
    def get_symbol_info(cls, symbol: str) -> dict:
        """
        Get comprehensive information about a symbol.

        Args:
            symbol: Symbol to analyze

        Returns:
            Dictionary with symbol information
        """
        if not symbol:
            return {'original': symbol, 'type': 'unknown'}

        symbol = symbol.upper().strip()

        info = {
            'original': symbol,
            'normalized': cls.normalize_symbol(symbol),
            'schwab_format': cls.to_schwab_format(symbol),
            'yahoo_format': cls.to_yahoo_format(symbol),
            'is_futures': cls.is_futures_symbol(symbol),
            'is_index': cls.is_index_symbol(symbol),
            'type': 'unknown'
        }

        if info['is_futures']:
            info['type'] = 'futures'
        elif info['is_index']:
            info['type'] = 'index'
        else:
            info['type'] = 'equity'

        return info

# Global instance for easy access
ticker_converter = TickerConverter()

def convert_to_schwab(symbol: str) -> str:
    """Convenience function to convert symbol to Schwab format."""
    return ticker_converter.to_schwab_format(symbol)

def convert_to_yahoo(symbol: str) -> str:
    """Convenience function to convert symbol to Yahoo format."""
    return ticker_converter.to_yahoo_format(symbol)

def get_symbol_info(symbol: str) -> dict:
    """Convenience function to get symbol information."""
    return ticker_converter.get_symbol_info(symbol)
