# TradingView to Yahoo Finance Ticker Conversion

This document provides the complete conversion mapping between TradingView futures symbols and their corresponding Yahoo Finance symbols.

## Continuous Contracts (1! format)

| TradingView | Yahoo Finance | Description |
|-------------|---------------|-------------|
| `ES1!` | `ES=F` | E-mini S&P 500 |
| `MES1!` | `ES=F` | Micro E-mini S&P 500 |
| `NQ1!` | `NQ=F` | E-mini NASDAQ-100 |
| `MNQ1!` | `NQ=F` | Micro E-mini NASDAQ-100 |
| `YM1!` | `YM=F` | E-mini Dow Jones |
| `MYM1!` | `YM=F` | Micro E-mini Dow Jones |
| `RTY1!` | `RTY=F` | E-mini Russell 2000 |
| `M2K1!` | `RTY=F` | Micro E-mini Russell 2000 |

### Energy
| TradingView | Yahoo Finance | Description |
|-------------|---------------|-------------|
| `CL1!` | `CL=F` | Crude Oil |
| `MCL1!` | `CL=F` | Micro Crude Oil |
| `NG1!` | `NG=F` | Natural Gas |
| `RB1!` | `RB=F` | RBOB Gasoline |
| `HO1!` | `HO=F` | Heating Oil |

### Metals
| TradingView | Yahoo Finance | Description |
|-------------|---------------|-------------|
| `GC1!` | `GC=F` | Gold |
| `MGC1!` | `GC=F` | Micro Gold |
| `SI1!` | `SI=F` | Silver |
| `HG1!` | `HG=F` | Copper |
| `PA1!` | `PA=F` | Palladium |
| `PL1!` | `PL=F` | Platinum |

### Treasuries
| TradingView | Yahoo Finance | Description |
|-------------|---------------|-------------|
| `ZB1!` | `ZB=F` | 30-Year Treasury Bond |
| `ZN1!` | `ZN=F` | 10-Year Treasury Note |
| `ZF1!` | `ZF=F` | 5-Year Treasury Note |
| `ZT1!` | `ZT=F` | 2-Year Treasury Note |
| `UB1!` | `UB=F` | Ultra Treasury Bond |

### Agriculture
| TradingView | Yahoo Finance | Description |
|-------------|---------------|-------------|
| `ZC1!` | `ZC=F` | Corn |
| `ZS1!` | `ZS=F` | Soybeans |
| `ZW1!` | `ZW=F` | Wheat |
| `ZM1!` | `ZM=F` | Soybean Meal |
| `ZL1!` | `ZL=F` | Soybean Oil |
| `ZO1!` | `ZO=F` | Oats |
| `ZR1!` | `ZR=F` | Rough Rice |
| `KC1!` | `KC=F` | Coffee |
| `SB1!` | `SB=F` | Sugar |
| `CT1!` | `CT=F` | Cotton |
| `CC1!` | `CC=F` | Cocoa |
| `LB1!` | `LB=F` | Lumber |

### Livestock
| TradingView | Yahoo Finance | Description |
|-------------|---------------|-------------|
| `HE1!` | `HE=F` | Lean Hogs |
| `LE1!` | `LE=F` | Live Cattle |
| `GF1!` | `GF=F` | Feeder Cattle |

### Currency
| TradingView | Yahoo Finance | Description |
|-------------|---------------|-------------|
| `DX1!` | `DX=F` | US Dollar Index |
| `EUR1!` | `6E=F` | Euro |
| `GBP1!` | `6B=F` | British Pound |
| `JPY1!` | `6J=F` | Japanese Yen |
| `CAD1!` | `6C=F` | Canadian Dollar |
| `CHF1!` | `6S=F` | Swiss Franc |
| `AUD1!` | `6A=F` | Australian Dollar |

### Crypto
| TradingView | Yahoo Finance | Description |
|-------------|---------------|-------------|
| `BTC1!` | `BTC=F` | Bitcoin Futures |
| `ETH1!` | `ETH=F` | Ethereum Futures |

### Volatility
| TradingView | Yahoo Finance | Description |
|-------------|---------------|-------------|
| `VIX1!` | `^VIX` | VIX Index |

## Specific Contract Months (.cme format)

For specific contract months, Yahoo Finance uses lowercase with `.cme` suffix:

### Pattern: `[BASE][MONTH][YEAR]` → `[base][month][yy].cme`

| TradingView | Yahoo Finance | Description |
|-------------|---------------|-------------|
| `ESU2025` | `esu25.cme` | E-mini S&P 500 Sep 2025 |
| `ESZ2025` | `esz25.cme` | E-mini S&P 500 Dec 2025 |
| `ESH2025` | `esh25.cme` | E-mini S&P 500 Mar 2025 |
| `ESM2025` | `esm25.cme` | E-mini S&P 500 Jun 2025 |
| `MESU2025` | `mesu25.cme` | Micro E-mini S&P 500 Sep 2025 |
| `MESZ2025` | `mesz25.cme` | Micro E-mini S&P 500 Dec 2025 |
| `MESH2025` | `mesh25.cme` | Micro E-mini S&P 500 Mar 2025 |
| `MESM2025` | `mesm25.cme` | Micro E-mini S&P 500 Jun 2025 |
| `NQU2025` | `nqu25.cme` | E-mini NASDAQ-100 Sep 2025 |
| `NQZ2025` | `nqz25.cme` | E-mini NASDAQ-100 Dec 2025 |
| `NQH2025` | `nqh25.cme` | E-mini NASDAQ-100 Mar 2025 |
| `NQM2025` | `nqm25.cme` | E-mini NASDAQ-100 Jun 2025 |
| `MNQU2025` | `mnqu25.cme` | Micro E-mini NASDAQ-100 Sep 2025 |
| `MNQZ2025` | `mnqz25.cme` | Micro E-mini NASDAQ-100 Dec 2025 |
| `MNQH2025` | `mnqh25.cme` | Micro E-mini NASDAQ-100 Mar 2025 |
| `MNQM2025` | `mnqm25.cme` | Micro E-mini NASDAQ-100 Jun 2025 |
| `EMU2025` | `emu25.cme` | Euro Sep 2025 |
| `EMZ2025` | `emz25.cme` | Euro Dec 2025 |
| `EMH2025` | `emh25.cme` | Euro Mar 2025 |
| `EMM2025` | `emm25.cme` | Euro Jun 2025 |

## Month Codes

| Code | Month | Code | Month |
|------|-------|------|-------|
| `F` | January | `N` | July |
| `G` | February | `Q` | August |
| `H` | March | `U` | September |
| `J` | April | `V` | October |
| `K` | May | `X` | November |
| `M` | June | `Z` | December |

## Examples

### Automatic Pattern Recognition
- `ESH2026` → `esh26.cme` (E-mini S&P 500 March 2026)
- `MESK2026` → `mesk26.cme` (Micro E-mini S&P 500 May 2026)
- `NQN2026` → `nqn26.cme` (E-mini NASDAQ-100 July 2026)

### Usage in Application
When using TradingView mode in universal controls:
1. Enter TradingView symbol: `MESU2025`
2. System converts to Yahoo Finance: `mesu25.cme`
3. Yahoo Finance data fetched for date synchronization
4. TradingView OHLC data fetched and aligned to Yahoo Finance dates
5. Result: TradingView prices with Yahoo Finance date consistency

---
*This conversion system ensures that TradingView and Yahoo Finance data use the same calendar days approach for consistent date ranges.*
