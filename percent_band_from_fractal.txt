// Using clusters find the most probable moves, and quantify them on the graph in
// terms of percentage probability.  Traders leave imprints of moves, footprints in the sand
// we use these imprints to provide areas of support and resistance.  This shows areas that
// traders like to buy and sell in terms of percentage move.  These ares are marked with black
// marks on the far right.  They tend to be grouped together forming clusters of areas.  The thicker
// the area the more support or resistance in that area.  Therefore we can quantify in percentage terms
// how often the price gets to that percentage move.  For example maybe 80% of the time the stock 
// off a low moves up 3% or 10%, and maybe 80% of the time the stock moves down -2.5% or -5%.
// Each stock is different so they all have different imprint levels.
// revised Jan 3, 2024

var select ="", symbol="";
var priceX, timer, lastsDT, lastS, rs;
var refreshcounter;
var rundatetime;
var datapull;
var elapsed;
var lastupdatedatetime;


	//----------------------------------------------------------------------------------------
	function Load()
	//----------------------------------------------------------------------------------------
	 {
		refreshcounter=0;
		rundatetime="";
		datapull=0;
		elapsed=0;

		symbol = GraphSymbol;
		if (symbol=="ES=F") {
			symbol="ES_F";
		}
		if (symbol=="NQ=F") {
			symbol="NQ_F";
		}

		priceX = -1;
		timer = -1;
		lastsDT = "";
		lastS = "";
		ClusterInit();	// this finds all the mathematical clusters
		PropertyAdd("Graph", "Preserve",PropertyTypeString);
		GraphHorizontalLines("False");

		idBull02=ControlLabel(0,80,"Bull2%",BorderStyleFixedSingle);
		ControlColor(idBull02,ColorWhite,ColorGreen);
		idBull10=ControlLabel(0,80,"Bull10%",BorderStyleFixedSingle);
		ControlColor(idBull10,ColorWhite,ColorGreen);
		idBull20=ControlLabel(0,80,"Bull20%",BorderStyleFixedSingle);
		ControlColor(idBull20,ColorWhite,ColorGreen);
		idBull50=ControlLabel(0,80,"Bull50%",BorderStyleFixedSingle);
		ControlColor(idBull50,ColorWhite,ColorGreen);
		idBull80=ControlLabel(0,80,"Bull80%",BorderStyleFixedSingle);
		ControlColor(idBull80,ColorWhite,ColorGreen);

		idBear02=ControlLabel(0,80,"Bear2%",BorderStyleFixedSingle);
		ControlColor(idBear02,ColorWhite,ColorRed);
		idBear10=ControlLabel(0,80,"Bear10%",BorderStyleFixedSingle);
		ControlColor(idBear10,ColorWhite,ColorRed);
		idBear20=ControlLabel(0,80,"Bear20%",BorderStyleFixedSingle);
		ControlColor(idBear20,ColorWhite,ColorRed);
		idBear50=ControlLabel(0,80,"Bear50%",BorderStyleFixedSingle);
		ControlColor(idBear50,ColorWhite,ColorRed);
		idBear80=ControlLabel(0,100,"Bear80%",BorderStyleFixedSingle);
		ControlColor(idBear80,ColorWhite,ColorRed);

		select = "SELECT TOP 1 Convert(varchar,RunDateTime,22) as rdt, Ceiling, Floor, PMLPrice, GreenLinePrice FROM signals_mm WHERE symbol='"+symbol+"'  ORDER BY RunDateTime DESC"

		//rs = dbGetData(select);
		rs = nothing;

	}

	//----------------------------------------------------------------------------------------
	// post draw waits until the graph has been fully built, then draws on top of everything
	function PostDraw()
	//----------------------------------------------------------------------------------------
	{
		SQLClearColumns();

		DrawRotateText(ColorBlack,FontImpact,FontPt16,GraphWidth/2,5,"Clusters : Percent Probable",Angle0)

		DrawPercentProbable(idBear80,-0.80,ColorRed,"Bear80Perc");	// -0.80 = beneath pivot line 80%
		DrawPercentProbable(idBear50,-0.50,ColorRed,"Bear50Perc");	// -0.50 = beneath pivot line 50%
		DrawPercentProbable(idBear20,-0.20,ColorRed,"Bear20Perc");	// -0.20 = beneath pivot line 20%
		DrawPercentProbable(idBear10,-0.10,ColorRed,"Bear10Perc");	// -0.10 = beneath pivot line 10%
		DrawPercentProbable(idBear02,-0.02,ColorRed,"Bear2Perc");	// -0.02 = beneath pivot line 2%



		DrawPercentProbable(idBull80,0.80,ColorGreen,"Bull80Perc");	// 0.80 = above pivot line 80%
		DrawPercentProbable(idBull50,0.50,ColorGreen,"Bull50Perc");	// 0.50 = above pivot line 50%
		DrawPercentProbable(idBull20,0.20,ColorGreen,"Bull20Perc");	// 0.20 = above pivot line 20%
		DrawPercentProbable(idBull10,0.10,ColorGreen,"Bull10Perc");	// 0.10 = above pivot line 10%
		DrawPercentProbable(idBull02,0.05,ColorGreen,"Bull5Perc");	// 0.02 = above pivot line 10%

		var PMLpriceX=0, PMLpriceY=0, rp;
		var CeilingPriceX=0, CeilingPriceY=0;
		var FloorPriceX=0, FloorPriceY=0;
		if (rs!="") {
			rp = dbGetItem(rs,"PMLPrice",1);
			cp = dbGetItem(rs,"Ceiling",1);
			fp = dbGetItem(rs,"Floor",1);
			rdt = dbGetItem(rs,"rdt",1);
			if (lastupdatedatetime!=rdt) { lastupdatedatetime=rdt; elapsed=0; }
			//DebugMsg("rp -> "+rp);
			PMLpriceX = GraphWidth-110
			PMLpriceY = ConvertPriceToPixelY(rp,GraphEndBar());
			CeilingPriceY = ConvertPriceToPixelY(cp,GraphEndBar());
			FloorPriceY = ConvertPriceToPixelY(fp,GraphEndBar());
			//DrawFillRectangle(ColorRed,PMLpriceX,PMLpriceY-1,108,2,NonTransparent)

			DrawRotateText(ColorBlack,FontImpact,FontPt12,GraphWidth/2,25,"{"+elapsed+" min} Ceiling,PML,Floor last update "+rdt,Angle0);
		}



		//--------- Draw the Moving Arrow ---------
		/*
		if (priceX<0 || priceX<=GraphWidth-120) {
			priceX = GraphWidth-60;
		} else {
			priceX -= 10;
		}
		*/
		sDT = GraphDateTime(GraphEndBar());
		if (timer<1 || lastsDT!=sDT) {
			timer = 1;
			lastsDT = sDT;
		} else { ++timer };
		spriceY = GraphLastSellPrice(GraphEndBar());
		priceY = ConvertPriceToPixelY(spriceY,GraphEndBar());
		priceX = GraphWidth-110
		DrawFillRectangle(ColorWhite,priceX,priceY-8,108,17,NonTransparent)
		DrawRectangle(ColorBlack,1, priceX,priceY-8,108,17)
		if (lastS=="") {
			lastS = "<=$"+getDecimalPointsWithCommas(spriceY)+" ["+timer+"]"
			DrawText(priceX,priceY-8,lastS);
		} else {
			lastS = "    $"+getDecimalPointsWithCommas(spriceY)+" ["+timer+"]"
			DrawText(priceX,priceY-8,lastS);
			lastS = "";	
		}
			if (PMLpriceY!=0){
				DrawFillRectangle(ColorRed,PMLpriceX,PMLpriceY-1,108,2,NonTransparent)
				DrawText(PMLpriceX,PMLpriceY-18,"PML "+rp);

			}
			if (CeilingPriceY!=0){
				DrawFillRectangle(ColorRed,PMLpriceX,CeilingPriceY-1,108,2,NonTransparent)
				DrawText(PMLpriceX,CeilingPriceY-18,"Ceiling "+cp);

				DrawFillRectangle(ColorGreen,PMLpriceX,FloorPriceY-1,108,2,NonTransparent)
				DrawText(PMLpriceX,FloorPriceY-18,"Floor "+fp);
			}



	}

	//----------------------------------------------------------------------------------------
	// format the price number with commas and a decimal point
	function getDecimalPointsWithCommas(amount) 
	//----------------------------------------------------------------------------------------
	{
    		return amount.toLocaleString(undefined, { maximumFractionDigits: 2, minimumFractionDicits: 2 });
	}

	//----------------------------------------------------------------------------------------
	// draw the percent probable lines
	function DrawPercentProbable(id,p,clr,SQLColumn)
	//----------------------------------------------------------------------------------------
	{

		var v;
		var width = 100; // defines how much space in the white box

		v=ClusterNode(p) // return the pixel Y coordinate
		n = ConvertPixelYToPrice(v); // return the price at that pixel Y coordinate
		s = getDecimalPointsWithCommas(n); // format the price

		if (s.length<6) {
			width = 80;
		}
		if (s.length>=6) {
			width = 85;
		}
		if (s.length>=7) {
			width = 100;
		}

		DrawLine(clr,10, 0,v, GraphWidth,v,SemiTransparent,128);
		//DrawFillRectangle(ColorWhite,GraphWidth-(width+23),v-8,width,14,NonTransparent)
		sPrice=abs(p)*100+"%@"+s;
		//DrawText(GraphWidth-(width+20),v-8,sPrice);
		ControlLeftTop(id,0,v-8);
		ControlSetText(id,sPrice);
		//DrawRectangle(ColorBlack,1, GraphWidth-(width+23),v-8,width,14)

		SQLAddColumn(SQLColumn,s);
	}

	//----------------------------------------------------------------------------------------
	function Timer()
	//----------------------------------------------------------------------------------------
	{
		return;
		DebugMsg(refreshcounter);
		if (refreshcounter>=60 && symbol!="") {

			DebugMsg("SELECT TOP 1 . . . .");
			select = "SELECT TOP 1 Convert(varchar,RunDateTime,22) as rdt, Ceiling, Floor, PMLPrice, GreenLinePrice FROM signals_mm WHERE symbol='"+symbol+"'  ORDER BY RunDateTime DESC"

			rs = dbGetData(select);

			refreshcounter=0;
			datapull++;
			elapsed++;
		}
		refreshcounter++;
		RefreshGraph();
}
